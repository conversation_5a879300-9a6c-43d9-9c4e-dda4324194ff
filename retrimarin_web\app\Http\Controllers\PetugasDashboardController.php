<?php

namespace App\Http\Controllers;

use App\Models\Retribusi;
use App\Models\Kapal;
use App\Models\Company;
use App\Models\Tarif;
use App\Models\MetodePembayaran;
use App\Services\MidtransService;
use App\Services\WhatsappGatewayService;
use App\Mail\RetribusiCreatedMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class PetugasDashboardController extends Controller
{
    /**
     * Show petugas dashboard.
     */
    public function index()
    {
        $petugas = Auth::guard('petugas')->user();

        // Get statistics
        $totalRetribusi = Retribusi::where('petugas_id', $petugas->id)->count();
        $totalPendapatan = Retribusi::where('petugas_id', $petugas->id)
            ->where('status_pembayaran', 'lunas')
            ->sum('total_harga');
        $retribusiPending = Retribusi::where('petugas_id', $petugas->id)
            ->where('status_pembayaran', 'pending')
            ->count();
        $retribusiLunas = Retribusi::where('petugas_id', $petugas->id)
            ->where('status_pembayaran', 'lunas')
            ->count();

        // Get recent retribusi
        $recentRetribusi = Retribusi::with(['kapal', 'company', 'tarif', 'metodePembayaran'])
            ->where('petugas_id', $petugas->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('petugas.dashboard', compact(
            'petugas',
            'totalRetribusi',
            'totalPendapatan',
            'retribusiPending',
            'retribusiLunas',
            'recentRetribusi'
        ));
    }

    /**
     * Show form to create new retribusi.
     */
    public function createRetribusi()
    {
        // Don't load all ships and tarifs by default - they will be loaded dynamically
        $companies = Company::active()->orderBy('nama_perusahaan')->get();
        $metodePembayarans = MetodePembayaran::active()->orderBy('tipe')->orderBy('nama_metode')->get();

        return view('petugas.retribusi.create', compact(
            'companies',
            'metodePembayarans'
        ));
    }

    /**
     * Store new retribusi.
     */
    public function storeRetribusi(Request $request)
    {
        $request->validate([
            'jenis_pembayar' => 'required|in:pribadi,perusahaan',
            'kapal_id' => 'required|exists:kapals,id',
            'company_id' => 'required_if:jenis_pembayar,perusahaan|nullable|exists:companies,id',
            'tarif_id' => 'required|exists:tarifs,id',
            'metode_pembayaran_id' => 'required|exists:metode_pembayarans,id',
            'jumlah' => 'required|numeric|min:0.01',
            'keterangan' => 'nullable|string',
        ]);

        try {
            $petugas = Auth::guard('petugas')->user();
            $tarif = Tarif::findOrFail($request->tarif_id);
            $metodePembayaran = MetodePembayaran::findOrFail($request->metode_pembayaran_id);

            $data = $request->all();
            $data['petugas_id'] = $petugas->id;
            $data['nomor_retribusi'] = Retribusi::generateNomorRetribusi();
            $data['harga_satuan'] = $tarif->harga;
            $data['total_harga'] = $request->jumlah * $tarif->harga;
            $data['tanggal_transaksi'] = now();
            $data['status_pembayaran'] = 'pending'; // All payments start as pending
            $data['tanggal_pembayaran'] = null; // Will be set when manually confirmed

            // Set company_id to null if jenis_pembayar is pribadi
            if ($request->jenis_pembayar == 'pribadi') {
                $data['company_id'] = null;
            }

            $retribusi = Retribusi::create($data);
            $retribusi->load(['petugas', 'company', 'kapal', 'tarif', 'metodePembayaran']);

            $midtransResponse = null;

            // Handle Midtrans payment if selected
            if ($metodePembayaran->tipe == 'midtrans') {
                try {
                    $midtransService = new MidtransService();
                    if ($midtransService->isConfigured()) {
                        $midtransResponse = $midtransService->createTransaction($retribusi);
                        Log::info('Midtrans transaction created (web)', [
                            'retribusi_id' => $retribusi->id,
                            'transaction_id' => $midtransResponse->transaction_id ?? null,
                            'status' => $midtransResponse->transaction_status ?? null
                        ]);
                    } else {
                        Log::warning('Midtrans not configured but payment method is midtrans (web)', [
                            'retribusi_id' => $retribusi->id
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Midtrans transaction failed (web)', [
                        'retribusi_id' => $retribusi->id,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the entire process, just log the error
                }
            }

            // Send email notification if company email exists
            if ($retribusi->company && $retribusi->company->email) {
                try {
                    Mail::to($retribusi->company->email)->send(new RetribusiCreatedMail($retribusi, $midtransResponse));
                    Log::info('Email notification sent (web)', [
                        'retribusi_id' => $retribusi->id,
                        'email' => $retribusi->company->email
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to send email notification (web)', [
                        'retribusi_id' => $retribusi->id,
                        'email' => $retribusi->company->email,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the entire process, just log the error
                }
            }

            // Send WhatsApp notification if company phone exists
            if ($retribusi->company && $retribusi->company->telepon) {
                try {
                    $whatsappService = new WhatsappGatewayService();
                    if ($whatsappService->isConfigured()) {
                        $whatsappService->sendRetribusiNotification($retribusi, 'created');
                        Log::info('WhatsApp notification sent (web)', [
                            'retribusi_id' => $retribusi->id,
                            'phone' => $retribusi->company->telepon
                        ]);
                    } else {
                        Log::warning('WhatsApp Gateway not configured (web)', [
                            'retribusi_id' => $retribusi->id
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to send WhatsApp notification (web)', [
                        'retribusi_id' => $retribusi->id,
                        'phone' => $retribusi->company->telepon,
                        'error' => $e->getMessage()
                    ]);
                    // Don't fail the entire process, just log the error
                }
            }

            $successMessage = 'Retribusi berhasil dibuat dengan nomor: ' . $retribusi->nomor_retribusi . '. Status pembayaran: PENDING - silakan konfirmasi pembayaran secara manual.';
            if ($midtransResponse && isset($midtransResponse->actions) && is_array($midtransResponse->actions)) {
                $successMessage .= ' Link pembayaran Midtrans telah dibuat.';
            }

            return redirect()->route('petugas.dashboard')
                ->with('success', $successMessage);
        } catch (\Exception $e) {
            Log::error('Failed to create retribusi (web)', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->route('petugas.dashboard')
                ->with('error', 'Gagal membuat retribusi: ' . $e->getMessage());
        }
    }

    /**
     * Get tarif details via AJAX.
     */
    public function getTarifDetails(Request $request)
    {
        $tarif = Tarif::find($request->tarif_id);

        if (!$tarif) {
            return response()->json(['error' => 'Tarif tidak ditemukan'], 404);
        }

        return response()->json([
            'harga' => $tarif->harga,
            'satuan' => $tarif->satuan,
            'formatted_harga' => $tarif->formatted_harga
        ]);
    }

    /**
     * Get ships based on payment type (pribadi/perusahaan) and company_id.
     */
    public function getShipsByPaymentType(Request $request)
    {
        $jenisPembayar = $request->get('jenis_pembayar');
        $companyId = $request->get('company_id');

        $query = Kapal::with('kategoriKapal')->active()->orderBy('nama_kapal');

        if ($jenisPembayar === 'pribadi') {
            // For pribadi, get ships with null company_id
            $query->whereNull('company_id');
        } elseif ($jenisPembayar === 'perusahaan' && $companyId) {
            // For perusahaan, get ships with matching company_id
            $query->where('company_id', $companyId);
        } else {
            // Return empty result if invalid parameters
            return response()->json([]);
        }

        $kapals = $query->get(['id', 'nama_kapal', 'nomor_imo', 'kategori_kapal_id']);

        // Add kategori information to the response
        $kapals = $kapals->map(function ($kapal) {
            return [
                'id' => $kapal->id,
                'nama_kapal' => $kapal->nama_kapal,
                'nomor_imo' => $kapal->nomor_imo,
                'kategori_kapal_id' => $kapal->kategori_kapal_id,
                'kategori_nama' => $kapal->kategoriKapal ? $kapal->kategoriKapal->nama_kategori : null
            ];
        });

        return response()->json($kapals);
    }

    /**
     * Get tarifs based on ship category.
     */
    public function getTarifsByShipCategory(Request $request)
    {
        try {
            // Ensure petugas is authenticated
            $petugas = Auth::guard('petugas')->user();
            if (!$petugas) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $kapalId = $request->get('kapal_id');

            if (!$kapalId) {
                return response()->json([]);
            }

            $kapal = Kapal::with('kategoriKapal')->find($kapalId);

            if (!$kapal) {
                return response()->json([]);
            }

            $query = Tarif::active()->orderBy('jenis_tarif')->orderBy('nama_tarif');

            if ($kapal->kategori_kapal_id) {
                // If ship has a category, get tarifs that apply to this category
                $query->whereHas('kategoriKapals', function ($q) use ($kapal) {
                    $q->where('kategori_kapal_id', $kapal->kategori_kapal_id);
                });
            } else {
                // If ship has no category, get tarifs that have no categories (apply to all)
                $query->whereDoesntHave('kategoriKapals');
            }

            $tarifs = $query->get(['id', 'nama_tarif', 'jenis_tarif', 'satuan', 'harga']);

            // Format the response
            $tarifs = $tarifs->map(function ($tarif) {
                return [
                    'id' => $tarif->id,
                    'nama_tarif' => $tarif->nama_tarif,
                    'jenis_tarif' => $tarif->jenis_tarif,
                    'jenis_tarif_label' => $tarif->jenis_tarif_label,
                    'satuan' => $tarif->satuan,
                    'harga' => $tarif->harga,
                    'formatted_harga' => $tarif->formatted_harga,
                    'display_text' => $tarif->nama_tarif . ' (' . $tarif->jenis_tarif_label . ') - ' . $tarif->formatted_harga . '/' . $tarif->satuan
                ];
            });

            return response()->json($tarifs);
        } catch (\Exception $e) {
            Log::error('Error in getTarifsByShipCategory: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load tarifs'], 500);
        }
    }

    /**
     * Show retribusi details.
     */
    public function showRetribusi(Retribusi $retribusi)
    {
        $petugas = Auth::guard('petugas')->user();

        // Check if petugas is authenticated
        if (!$petugas) {
            return redirect()->route('petugas.login')
                ->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Ensure petugas can only view their own retribusi
        if ($retribusi->petugas_id != $petugas->id) {
            return redirect()->route('petugas.dashboard')
                ->with('error', 'Anda tidak memiliki akses untuk melihat retribusi ini. Anda hanya dapat melihat retribusi yang Anda buat sendiri.');
        }

        $retribusi->load(['kapal', 'company', 'tarif', 'metodePembayaran']);

        return view('petugas.retribusi.show', compact('retribusi'));
    }

    /**
     * Update payment status to lunas.
     */
    public function markAsLunas(Retribusi $retribusi)
    {
        $petugas = Auth::guard('petugas')->user();

        // Check if petugas is authenticated
        if (!$petugas) {
            return redirect()->route('petugas.login')
                ->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Ensure petugas can only update their own retribusi
        if ($retribusi->petugas_id != $petugas->id) {
            return redirect()->back()
                ->with('error', 'Anda tidak memiliki akses untuk mengubah retribusi ini. Anda hanya dapat mengubah retribusi yang Anda buat sendiri.');
        }

        if ($retribusi->status_pembayaran === 'pending') {
            $retribusi->update([
                'status_pembayaran' => 'lunas',
                'tanggal_pembayaran' => now()
            ]);

            return redirect()->back()
                ->with('success', 'Status pembayaran berhasil diubah menjadi lunas.');
        }

        return redirect()->back()
            ->with('error', 'Status pembayaran tidak dapat diubah. Hanya retribusi dengan status pending yang dapat diubah menjadi lunas.');
    }

    /**
     * Cancel retribusi.
     */
    public function cancelRetribusi(Retribusi $retribusi)
    {
        $petugas = Auth::guard('petugas')->user();

        // Check if petugas is authenticated
        if (!$petugas) {
            return redirect()->route('petugas.login')
                ->with('error', 'Anda harus login terlebih dahulu.');
        }

        // Ensure petugas can only cancel their own retribusi
        if ($retribusi->petugas_id != $petugas->id) {
            return redirect()->back()
                ->with('error', 'Anda tidak memiliki akses untuk membatalkan retribusi ini. Anda hanya dapat membatalkan retribusi yang Anda buat sendiri.');
        }

        if ($retribusi->status_pembayaran === 'pending') {
            $retribusi->update([
                'status_pembayaran' => 'batal'
            ]);

            return redirect()->back()
                ->with('success', 'Retribusi berhasil dibatalkan.');
        }

        return redirect()->back()
            ->with('error', 'Retribusi tidak dapat dibatalkan. Hanya retribusi dengan status pending yang dapat dibatalkan.');
    }

    /**
     * List all retribusi for current petugas.
     */
    public function listRetribusi(Request $request)
    {
        $petugas = Auth::guard('petugas')->user();

        $query = Retribusi::with(['kapal', 'company', 'tarif', 'metodePembayaran'])
            ->where('petugas_id', $petugas->id);

        // Filter by status if provided
        if ($request->filled('status')) {
            $query->where('status_pembayaran', $request->status);
        }

        // Filter by date range if provided
        if ($request->filled('start_date')) {
            $query->whereDate('tanggal_transaksi', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('tanggal_transaksi', '<=', $request->end_date);
        }

        $retribusis = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('petugas.retribusi.index', compact('retribusis'));
    }
}
